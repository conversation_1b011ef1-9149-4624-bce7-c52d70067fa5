import { CodeBlock } from "@/components/site/code-block";
import { ComponentRenderer } from "@/components/site/component-renderer";
import { Cli } from "@/components/site/cli";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ist,
  Ta<PERSON>Trigger,
  Ta<PERSON>Content,
} from "@/components/site/tabs.tsx";
import { PropsTable } from "@/components/site/props-table.tsx";
import { Table, StatusBadge } from "@/components/ui/table";
import Install from "./install.tsx";

<Install />

# Vite

Install and configure shadcn/ui for Vite.

# Create project

Start by creating a new React project using `vite`. Select the `React + TypeScript` template:

<PMTabs>
  <PNPMTabContent>
    ```bash
    pnpm create vite@latest
    ```
  </PNPMTabContent>
  <NPMTabContent>
    ```bash
    npm create vite@latest
    ```
  </NPMTabContent>
  <YarnTabContent>
     ```bash
    yarn create vite@latest
    ```
  </YarnTabContent>
  <BunTabContent>
     ```bash
    bun create vite@latest
    ```
  </BunTabContent>
</PMTabs>

## Add Tailwind CSS

<PMTabs>
  <PNPMTabContent>
    ```bash
    pnpm add tailwindcss @tailwindcss/vite
    ```
  </PNPMTabContent>
  <NPMTabContent>
    ```bash
    npm install tailwindcss @tailwindcss/vite
    ```
  </NPMTabContent>
  <YarnTabContent>
    ```bash
    yarn add tailwindcss @tailwindcss/vite
    ```
  </YarnTabContent>
  <BunTabContent>
    ```bash
    bun add tailwindcss @tailwindcss/vite
    ```
  </BunTabContent>
</PMTabs>

Replace everything in `src/index.css` with the following:

```css
@import "tailwindcss";
```

# Edit tsconfig.json file

The current version of Vite splits TypeScript configuration into three files, two of which need to be edited. Add the `baseUrl` and `paths` properties to the `compilerOptions` section of the `tsconfig.json` and `tsconfig.app.json` files:

```json
{
  "files": [],
  "references": [
    {
      "path": "./tsconfig.app.json"
    },
    {
      "path": "./tsconfig.node.json"
    }
  ],
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

# Edit tsconfig.app.json file

Add the following code to the `tsconfig.app.json` file to resolve paths, for your IDE:

```json
{
  "compilerOptions": {
    // ...
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ]
    }
    // ...
  }
}
```

# Update vite.config.ts

Add the following code to the `vite.config.ts` so your app can resolve paths without error:


<PMTabs>
  <PNPMTabContent>
    ```bash
     pnpm add -D @types/node
    ```
  </PNPMTabContent>
  <NPMTabContent>
    ```bash
    npm install -D @types/node
    ```
  </NPMTabContent>
  <YarnTabContent>
    ```bash
    yarn add -D @types/node
    ```
  </YarnTabContent>
  <BunTabContent>
    ```bash
    bun add -D @types/node
    ```
  </BunTabContent>
</PMTabs>





Now, update your `vite.config.ts` file:

```typescript
import path from "path"
import tailwindcss from "@tailwindcss/vite"
import react from "@vitejs/plugin-react"
import { defineConfig } from "vite"

// [https://vite.dev/config/](https://vite.dev/config/)
export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
})
```

# Run the CLI

Run the `shadcn` init command to setup your project:

<PMTabs>
  <PNPMTabContent>
    ```bash
    pnpm dlx shadcn@latest init
    ```
  </PNPMTabContent>
  <NPMTabContent>
    ```bash
    npx shadcn@latest init
    ```
  </NPMTabContent>
  <YarnTabContent>
    ```bash
    yarn dlx shadcn@latest init
    ```
  </YarnTabContent>
  <BunTabContent>
    ```bash
    bunx --bun shadcn@latest init
    ```
  </BunTabContent>
</PMTabs>

You will be asked a few questions to configure `components.json`.

```bash
Which color would you like to use as base color? › Neutral
```

# Add Components

You can now start adding components to your project.

<PMTabs>
  <PNPMTabContent>
    ```bash
    pnpm dlx shadcn@latest add button
    ```
  </PNPMTabContent>
  <NPMTabContent>
    ```bash
    npx shadcn@latest add button
    ```
  </NPMTabContent>
  <YarnTabContent>
    ```bash
    yarn dlx shadcn@latest add button
    ```
  </YarnTabContent>
  <BunTabContent>
    ```bash
    bunx --bun shadcn@latest add button
    ```
  </BunTabContent>
</PMTabs>

The command above will add the `Button` component to your project. You can then import it like this:

```tsx
import { Button } from "@/components/ui/button"

function App() {
  return (
    <div className="flex min-h-svh flex-col items-center justify-center">
      <Button>Click me</Button>
    </div>
  )
}

export default App
