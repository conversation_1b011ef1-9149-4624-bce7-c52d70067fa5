{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "accordion", "title": "Accordion", "description": "A modern, accessible accordion component with smooth animations for expanding and collapsing content sections. Ideal for FAQs, toggleable content sections, and collapsible lists in user interfaces.", "author": "<PERSON><PERSON><PERSON><PERSON>", "type": "registry:ui", "dependencies": [], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "accordion.tsx", "content": "'use client';\nimport React, { useState } from 'react';\n\n// --- Type Definitions for the component ---\ninterface AccordionItemData {\n  title: string;\n  content: string;\n}\n\ninterface AccordionProps {\n  items: AccordionItemData[];\n}\n\ninterface AccordionItemProps {\n  title: string;\n  content: string;\n  isOpen: boolean;\n  onClick: () => void;\n  isLast: boolean;\n}\n\n// --- Internal Accordion Item Component ---\nfunction AccordionItem({\n  title,\n  content,\n  isOpen,\n  onClick,\n  isLast,\n}: AccordionItemProps) {\n  // A unique ID for ARIA attributes\n  const uniqueId = title.replace(/\\s+/g, '-');\n\n  return (\n    <div\n      className={`${!isLast ? 'border-b border-gray-200 dark:border-slate-700' : ''}`}\n    >\n      <button\n        type='button'\n        className='w-full flex justify-between items-center p-5 text-left text-lg font-medium text-gray-800 dark:text-slate-200 hover:bg-gray-100 dark:hover:bg-slate-700/50 focus:outline-none focus-visible:ring focus-visible:ring-indigo-500 focus-visible:ring-opacity-75 transition-colors duration-300'\n        onClick={onClick}\n        aria-expanded={isOpen}\n        aria-controls={`accordion-content-${uniqueId}`}\n        id={`accordion-header-${uniqueId}`}\n      >\n        <span>{title}</span>\n        <div className='w-6 h-6 flex-shrink-0 flex items-center justify-center'>\n          <svg\n            className={`w-5 h-5 text-gray-500 dark:text-slate-400 transition-transform duration-300 ${isOpen ? 'rotate-45' : ''}`}\n            xmlns='http://www.w3.org/2000/svg'\n            fill='none'\n            viewBox='0 0 24 24'\n            strokeWidth={2}\n            stroke='currentColor'\n          >\n            <path\n              strokeLinecap='round'\n              strokeLinejoin='round'\n              d='M12 4.5v15m7.5-7.5h-15'\n            />\n          </svg>\n        </div>\n      </button>\n\n      <div\n        id={`accordion-content-${uniqueId}`}\n        role='region'\n        aria-labelledby={`accordion-header-${uniqueId}`}\n        className={`grid overflow-hidden transition-all duration-500 ease-in-out ${isOpen ? 'grid-rows-[1fr] opacity-100' : 'grid-rows-[0fr] opacity-0'}`}\n      >\n        <div className='overflow-hidden'>\n          <div className='p-5 pt-2 text-gray-600 dark:text-slate-400'>\n            <p>{content}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// --- Main Reusable Accordion Component ---\nexport default function Accordion({ items }: AccordionProps) {\n  const [openIndex, setOpenIndex] = useState<number | null>(null);\n\n  const handleClick = (index: number) => {\n    setOpenIndex(openIndex === index ? null : index);\n  };\n\n  return (\n    <div className='rounded-xl shadow-lg bg-gray-50 dark:bg-slate-800/60 border border-gray-200 dark:border-slate-700 backdrop-blur-sm'>\n      {items.map((item, index) => (\n        <AccordionItem\n          key={index}\n          title={item.title}\n          content={item.content}\n          isOpen={openIndex === index}\n          onClick={() => handleClick(index)}\n          isLast={index === items.length - 1}\n        />\n      ))}\n    </div>\n  );\n}", "type": "registry:ui"}]}