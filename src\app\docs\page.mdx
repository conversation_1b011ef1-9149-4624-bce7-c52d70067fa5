import { <PERSON><PERSON><PERSON> } from "@/components/site/code-block";
import { ComponentRenderer } from "@/components/site/component-renderer";
import { Cli } from "@/components/site/cli";
import {
  Ta<PERSON>,
  Ta<PERSON><PERSON>ist,
  Ta<PERSON>Trigger,
  TabsContent,
} from "@/components/site/tabs.tsx";
import { PropsTable } from "@/components/site/props-table.tsx";
import { Table, StatusBadge } from "@/components/ui/table";

# Sera UI

**Beautifully designed components you can copy and paste into your apps. Accessible. Customizable. Open Source.**

Sera UI is a reusable component library for React, Next.js, and other JSX-based libraries. Currently, we support **React** and **Next.js** only. Our goal is to create next-level UI with minimal code, optimal performance, and a modern feel.

## 💡 Why Sera UI?

> “I’m a JavaScript library creator from Bangladesh, not a professional designer. But after building several front-end projects, I realized I needed my own UI system to streamline everything.”

Sera UI is born from real development needs — built by a developer, for developers.

## ✨ Features

* 🎨 **Modern Design**: Clean and beautiful UI components.
* ⚡ **High Performance**: Built with performance and accessibility in mind.
* 🧱 **Easy to Customize**: Easily themeable with Tailwind CSS.
* 🔄 **Smooth Animations**: Powered by the excellent `framer-motion` library.
* 🧩 **Icons Included**: `lucide-react` icons are integrated out of the box.

## 🚀 Getting Started

You can add Sera UI components to your project in two ways: using our CLI for a quick setup or by manually copying the component code for more control.

# CLI Installation
Our CLI will guide you through the process of adding components to your project. Here is an example of how to add the `Tabs` component:



<PMTabs>
  <PNPMTabContent>
    ```bash
    pnpm dlx shadcn@latest add "http://localhost:3000/registry/tabs.json"
    ```
  </PNPMTabContent>
  <NPMTabContent>
    ```bash
    npx shadcn@latest add "http://localhost:3000/registry/tabs.json"
    ```
  </NPMTabContent>
  <YarnTabContent>
     ```bash
    yarn dlx shadcn@latest add "http://localhost:3000/registry/tabs.json"
    ```
  </YarnTabContent>
  <BunTabContent>
     ```bash
    bunx shadcn@latest add "http://localhost:3000/registry/tabs.json"
    ```
  </BunTabContent>
</PMTabs>



### Manual Installation

For manual installation, you can copy and paste the component code directly from our documentation into your project.

1. **Choose a Component**: Browse our component library and find what you need.
2. **Copy the Code**: You can view and copy the source code for each component.
3. **Paste and Customize**: Paste the code into your project and customize it to fit your needs.
