{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "tabs classic", "title": "tabs classic", "description": "A minimalistic tab component designed with React and Tailwind CSS.", "author": "<PERSON><PERSON><PERSON><PERSON>", "type": "registry:ui", "dependencies": [], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "tabs-classic.tsx", "content": "'use client';\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nexport default function TabsViewClassic() {\n  const [activeTab, setActiveTab] = useState(1);\n  const [isHovering, setIsHovering] = useState<number | null>(null);\n\n  const tabs = [\n    {\n      id: 1,\n      name: 'Photos',\n      icon: '📸',\n      color: 'from-pink-500 to-rose-500',\n      content:\n        'This is the PHOTOS tab content. Here you would display your photo gallery or image collection.',\n    },\n    {\n      id: 2,\n      name: 'Music',\n      icon: '🎵',\n      color: 'from-purple-500 to-indigo-500',\n      content:\n        'This is the MUSIC tab content. Here you would display your music player or audio tracks.',\n    },\n    {\n      id: 3,\n      name: 'Videos',\n      icon: '🎬',\n      color: 'from-blue-500 to-cyan-500',\n      content:\n        'This is the VIDEOS tab content. Here you would display your video player or video collection.',\n    },\n  ];\n\n  return (\n    <div className='w-full max-w-2xl mx-auto'>\n      {/* Tabs layout with cards */}\n      <div className='grid grid-cols-3 gap-4 mb-8'>\n        {tabs.map((tab) => (\n          <motion.div\n            key={tab.id}\n            onClick={() => setActiveTab(tab.id)}\n            onMouseEnter={() => setIsHovering(tab.id)}\n            onMouseLeave={() => setIsHovering(null)}\n            className={`relative overflow-hidden rounded-xl cursor-pointer ${\n              activeTab === tab.id\n                ? `bg-gradient-to-br ${tab.color} shadow-lg`\n                : 'bg-white dark:bg-gray-800 shadow hover:shadow-md'\n            }`}\n            whileHover={{ y: -5, transition: { duration: 0.2 } }}\n            whileTap={{ scale: 0.98 }}\n            layout\n          >\n            <div\n              className={`p-4 flex flex-col items-center justify-center aspect-[4/3] ${\n                activeTab === tab.id\n                  ? 'text-white'\n                  : 'text-gray-800 dark:text-gray-200'\n              }`}\n            >\n              <motion.div\n                className='text-4xl mb-2'\n                initial={false}\n                animate={{\n                  scale:\n                    isHovering === tab.id || activeTab === tab.id ? 1.2 : 1,\n                  rotate:\n                    isHovering === tab.id && activeTab !== tab.id ? 10 : 0,\n                }}\n                transition={{ type: 'spring', stiffness: 400, damping: 17 }}\n              >\n                {tab.icon}\n              </motion.div>\n              <span className='font-medium tracking-wide text-center'>\n                {tab.name}\n              </span>\n            </div>\n\n            {isHovering === tab.id && activeTab !== tab.id && (\n              <motion.div\n                className={`absolute inset-0 bg-gradient-to-br opacity-20 ${tab.color}`}\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 0.2 }}\n                exit={{ opacity: 0 }}\n                transition={{ duration: 0.3 }}\n                style={{\n                  backgroundImage: `linear-gradient(to bottom right, var(--tw-gradient-stops))`,\n                }}\n              />\n            )}\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Content Area with dynamic colors based on active tab */}\n      <AnimatePresence mode='wait'>\n        <motion.div\n          key={activeTab}\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: -20 }}\n          transition={{ duration: 0.3 }}\n          className={`relative overflow-hidden rounded-xl p-6 bg-white dark:bg-gray-800 shadow-lg border-t-4 border-gradient-to-r ${tabs.find((t) => t.id === activeTab)?.color}`}\n        >\n          <div className='absolute top-0 right-0 p-2 flex items-center justify-center'>\n            <motion.div\n              className='text-2xl opacity-50'\n              animate={{\n                rotate: [0, 10, 0, -10, 0],\n                scale: [1, 1.2, 1, 1.2, 1],\n              }}\n              transition={{\n                duration: 2,\n                ease: 'easeInOut',\n                repeat: Infinity,\n                repeatDelay: 3,\n              }}\n            >\n              {tabs.find((t) => t.id === activeTab)?.icon}\n            </motion.div>\n          </div>\n\n          <h3 className='text-xl font-bold mb-4 text-gray-800 dark:text-white'>\n            {tabs.find((t) => t.id === activeTab)?.name}\n          </h3>\n\n          <div className='prose dark:prose-invert'>\n            <p>{tabs.find((t) => t.id === activeTab)?.content}</p>\n          </div>\n\n          <div className='mt-6 text-center'>\n            <button\n              className={`px-4 py-2 rounded-full text-white bg-gradient-to-r ${tabs.find((t) => t.id === activeTab)?.color}`}\n            >\n              Explore {tabs.find((t) => t.id === activeTab)?.name}\n            </button>\n          </div>\n        </motion.div>\n      </AnimatePresence>\n    </div>\n  );\n}\n", "type": "registry:ui"}]}