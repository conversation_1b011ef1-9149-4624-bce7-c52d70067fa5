import { CodeBlock } from "@/components/site/code-block";
import { ComponentRenderer } from "@/components/site/component-renderer";
import { Cli } from "@/components/site/cli";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@/components/site/tabs.tsx";
import { PropsTable } from "@/components/site/props-table.tsx";
import { Table, StatusBadge } from "@/components/ui/table";
import TabsView from "./tabs-view.tsx";


# 🎛️ Tabs Component

A minimalistic and animated tab component built with **React**, **Tailwind CSS**, and powered by **Framer Motion** for smooth transitions.


# Introduction

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<TabsView />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/tabs/tabs-view.tsx" />
  </TabsContent>
</Tabs>


# Installation

<Tabs defaultValue="cli" className="">
  <TabsList>
    <TabsTrigger value="cli">CLI</TabsTrigger>
    <TabsTrigger value="manual">Manual</TabsTrigger>
  </TabsList>
  <TabsContent value="cli">
    <Cli command={`add tabs.json`} />
  </TabsContent>

  <TabsContent value="manual">
    <CodeBlock filePath="src/components/core/tabs.tsx" />
  </TabsContent>
</Tabs>

# 🚀 Usage

Here's how to use the `Tabs` component in your project:

# 🛠️ Installation

You can manually copy the component into your project, **or** use the top-side CLI to install it automatically.

```bash
npx shadcn@latest add "https://seraui.seraprogrammer.com/registry/tabs.json"
```

> Once installed, you can use it anywhere in your app like this:

## ✨ Example Use Case

Use the `Tabs` component to organize different sections of content into a clean, navigable interface.

# 🧩 Code Example

```tsx
import Tabs from "@/components/ui/tabs";

export default function TabsView() {
  const tabData = [
    {
      id: 1,
      name: 'Photos',
      content:
        'This is the PHOTOS tab content. Here you would display your photo gallery or image collection.',
    },
    {
      id: 2,
      name: 'Music',
      content:
        'This is the MUSIC tab content. Here you would display your music player or audio tracks.',
    },
    {
      id: 3,
      name: 'Videos',
      content:
        'This is the VIDEOS tab content. Here you would display your video player or video collection.',
    },
  ];

  return <Tabs items={tabData} />;
}
```

---

✅ Just import the component and pass your desired tab data — the rest is handled with smooth transitions and a beautiful UI.

Need more examples or want to add custom icons or components inside tabs? Let me know!
