import Button from './button.tsx'

# Button

A versatile button component with multiple variants, sizes, loading states, and smooth ripple animations.

## Features

- **Multiple Variants**: Default, destructive, outline, secondary, ghost, and link styles
- **Flexible Sizing**: Small, default, and large sizes
- **Loading States**: Built-in loading spinner with disabled interaction
- **Ripple Animation**: Smooth click animations using Framer Motion
- **Accessibility**: Proper ARIA attributes and keyboard navigation
- **TypeScript**: Fully typed with comprehensive prop interfaces

## Installation

```bash
npm install framer-motion
```

## Usage

### Basic Examples

<div className="flex flex-wrap items-center justify-center gap-4 p-6 bg-gray-50 dark:bg-gray-900 rounded-lg">
  <Button variant="default">Default</Button>
  <Button variant="destructive">Destructive</Button>
  <Button variant="outline">Outline</Button>
  <Button variant="secondary">Secondary</Button>
  <Button variant="ghost">Ghost</Button>
  <Button variant="link">Link</Button>
</div>

### Sizes

<div className="flex flex-wrap items-center justify-center gap-4 p-6 bg-gray-50 dark:bg-gray-900 rounded-lg">
  <Button variant="default" size="sm">Small</Button>
  <Button variant="default" size="default">Default</Button>
  <Button variant="default" size="lg">Large</Button>
</div>

### Loading States

<div className="flex flex-wrap items-center justify-center gap-4 p-6 bg-gray-50 dark:bg-gray-900 rounded-lg">
  <Button variant="default" loading>Loading</Button>
  <Button variant="outline" loading>Loading</Button>
  <Button variant="destructive" loading>Loading</Button>
</div>

## API Reference

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `'default' \| 'destructive' \| 'outline' \| 'secondary' \| 'ghost' \| 'link'` | `'default'` | The visual style variant |
| `size` | `'default' \| 'sm' \| 'lg'` | `'default'` | The size of the button |
| `loading` | `boolean` | `false` | Shows loading spinner and disables interaction |
| `className` | `string` | - | Additional CSS classes |
| `children` | `ReactNode` | - | Button content |
| `onClick` | `(event: MouseEvent) => void` | - | Click event handler |

### Variants

- **default**: Primary button with dark background
- **destructive**: Red button for dangerous actions
- **outline**: Transparent button with border
- **secondary**: Subtle button with light background
- **ghost**: Minimal button with hover effects
- **link**: Text button with underline on hover

## Examples

### Custom Click Handler

```tsx
<Button
  variant="default"
  onClick={() => console.log('Button clicked!')}
>
  Click me
</Button>
```

### With Custom Styling

```tsx
<Button
  variant="outline"
  className="border-blue-500 text-blue-500 hover:bg-blue-50"
>
  Custom Button
</Button>
```

### Loading Button

```tsx
const [isLoading, setIsLoading] = useState(false);

<Button
  variant="default"
  loading={isLoading}
  onClick={() => setIsLoading(true)}
>
  Submit
</Button>
```