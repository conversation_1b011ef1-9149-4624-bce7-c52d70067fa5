import React, { useState } from 'react';
import { motion, MotionProps } from 'framer-motion';

// A typed utility for combining class names. In a real project, you'd use a library like `tailwind-merge`.
const cn = (...inputs: (string | undefined | null | false)[]): string => {
  return inputs.filter(Boolean).join(' ');
}

// SVG component for the loading spinner, a typed React Functional Component.
const Loader2: React.FC<{ className?: string }> = ({ className }) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={cn("animate-spin", className)}
    >
        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
    </svg>
);

// Define the types for variants and sizes explicitly
type ButtonVariant = 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
type ButtonSize = 'default' | 'sm' | 'lg';


// Define the TypeScript interface for the Button's props.
// We've added MotionProps to include animation props like `whileTap`.
export interface ButtonProps 
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
          MotionProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  loading?: boolean;
}

// Helper function to get the correct classes without cva
const getButtonClasses = ({ variant = 'default', size = 'default' }: Pick<ButtonProps, 'variant' | 'size'>) => {
    const baseClasses = "relative inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus:outline-none disabled:opacity-50 disabled:pointer-events-none overflow-hidden";

    const variantClasses = {
        default: "bg-slate-900 text-slate-50 hover:bg-slate-900/90 dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50/90",
        destructive: "bg-red-500 text-white hover:bg-red-600 dark:bg-red-600 dark:text-white dark:hover:bg-red-700",
        outline: "border border-slate-200 bg-transparent hover:bg-slate-100 text-slate-900 dark:border-slate-800 dark:hover:bg-slate-800 dark:text-slate-50",
        secondary: "bg-slate-100 text-slate-900 hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-50 dark:hover:bg-slate-700",
        ghost: "hover:bg-slate-100 text-slate-900 dark:hover:bg-slate-800 dark:text-slate-50",
        link: "text-slate-900 underline-offset-4 hover:underline dark:text-slate-50",
    };

    const sizeClasses = {
        default: "h-10 py-2 px-4",
        sm: "h-9 px-3 rounded-md",
        lg: "h-11 px-8 rounded-md",
    };

    return cn(baseClasses, variantClasses[variant], sizeClasses[size]);
};


// Define the type for an individual ripple object.
interface Ripple {
  x: number;
  y: number;
  size: number;
  id: number;
}

// The main Button component, now using React.forwardRef for better ref handling.
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, children, loading, onClick, ...props }, ref) => {
    // State is now typed with the Ripple interface.
    const [ripples, setRipples] = useState<Ripple[]>([]);

    // The click event is typed with React.MouseEvent.
    // This handler now also calls the onClick prop passed to the component.
    const createRipple = (event: React.MouseEvent<HTMLButtonElement>) => {
      if (loading) return;

      const button = event.currentTarget;
      const rect = button.getBoundingClientRect();
      const rippleSize = Math.max(rect.width, rect.height);
      const x = event.clientX - rect.left - rippleSize / 2;
      const y = event.clientY - rect.top - rippleSize / 2;

      const newRipple: Ripple = { x, y, size: rippleSize, id: Date.now() };

      setRipples(currentRipples => [...currentRipples, newRipple]);
      
      setTimeout(() => {
          setRipples(currentRipples => currentRipples.filter(r => r.id !== newRipple.id));
      }, 700);

      // Call the original onClick handler if it exists.
      onClick?.(event);
    };
    
    const rippleColor = (variant === 'default' || variant === 'destructive') 
      ? 'bg-white/30 dark:bg-slate-900/20' 
      : 'bg-slate-900/10 dark:bg-white/10';

    return (
      <motion.button
        className={cn(getButtonClasses({ variant, size }), className)}
        onClick={createRipple}
        whileTap={!loading ? { scale: 0.97 } : {}}
        disabled={loading}
        ref={ref} // Forward the ref to the button element.
        {...props}
      >
        <span className="relative z-10 flex items-center gap-2">
            {loading && <Loader2 className="h-4 w-4" />}
            {children}
        </span>
        
        {!loading && (
          <div className="absolute inset-0 z-0">
            {ripples.map((ripple) => (
              <motion.span
                key={ripple.id}
                className={cn("absolute rounded-full", rippleColor)}
                style={{ left: ripple.x, top: ripple.y, width: ripple.size, height: ripple.size }}
                initial={{ transform: 'scale(0)', opacity: 1 }}
                animate={{ transform: 'scale(2)', opacity: 0 }}
                transition={{ duration: 0.7, ease: 'easeOut' }}
              />
            ))}
          </div>
        )}
      </motion.button>
    );
  }
);
Button.displayName = "Button"; // Assigning a display name for better debugging.

// The main App component, now a typed functional component.
const App: React.FC = () => {
  return (
    <div className="flex h-screen w-full flex-col items-center justify-center gap-6 bg-white font-sans transition-colors dark:bg-slate-950">
      <div className="flex flex-wrap items-center justify-center gap-4">
          <Button variant="default">Default</Button>
          <Button variant="destructive">Destructive</Button>
          <Button variant="outline">Outline</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="ghost">Ghost</Button>
          <Button variant="link">Link</Button>
      </div>
      <div className="flex flex-wrap items-center justify-center gap-4">
          <Button variant="default" size="sm">Small</Button>
          <Button variant="default" size="lg">Large</Button>
          <Button variant="default" loading>Loading</Button>
          <Button variant="outline" loading>Loading</Button>
      </div>
    </div>
  );
}

export default App;
