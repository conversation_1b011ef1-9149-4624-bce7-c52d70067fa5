{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "tabs fancy", "title": "tabs fancy", "description": "A minimalistic tab component designed with React and Tailwind CSS.", "author": "<PERSON><PERSON><PERSON><PERSON>", "type": "registry:ui", "dependencies": [], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "tabs-fancy.tsx", "content": "'use client';\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nexport default function TabsViewFancy() {\n  const [activeTab, setActiveTab] = useState(1);\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Simulate loading effect when changing tabs\n  useEffect(() => {\n    if (activeTab) {\n      setIsLoading(true);\n      const timer = setTimeout(() => {\n        setIsLoading(false);\n      }, 400);\n      return () => clearTimeout(timer);\n    }\n  }, [activeTab]);\n\n  const tabs = [\n    {\n      id: 1,\n      name: 'Photos',\n      icon: '📸',\n      type: 'content',\n      content:\n        'This is the PHOTOS tab content. Here you would display your photo gallery or image collection.',\n    },\n    {\n      id: 2,\n      name: 'Music',\n      icon: '🎵',\n      type: 'content',\n      content:\n        'This is the MUSIC tab content. Here you would display your music player or audio tracks.',\n    },\n    {\n      id: 3,\n      name: 'Videos',\n      icon: '🎬',\n      type: 'content',\n      content:\n        'This is the VIDEOS tab content. Here you would display your video player or video collection.',\n    },\n  ];\n\n  return (\n    <div className='w-full max-w-2xl mx-auto'>\n      {/* Tabs Container - Vertical design for larger screens, horizontal for mobile */}\n      <div className='flex flex-col sm:flex-row gap-6 rounded-xl overflow-hidden'>\n        {/* Sidebar navigation */}\n        <div className='sm:w-56 flex sm:flex-col rounded-xl bg-black/5 dark:bg-white/5 backdrop-filter backdrop-blur-lg'>\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`\n                relative group flex items-center w-full px-4 py-3 sm:py-4 transition-all\n                ${\n                  activeTab === tab.id\n                    ? 'text-white dark:text-white'\n                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300'\n                }\n              `}\n            >\n              {/* Background highlight for active tab */}\n              {activeTab === tab.id && (\n                <motion.div\n                  layoutId='tabBackground'\n                  className='absolute inset-0 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg'\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ duration: 0.2 }}\n                />\n              )}\n\n              {/* Tab content with icon and text */}\n              <div className='flex items-center gap-3 z-10'>\n                <span className='text-xl'>{tab.icon}</span>\n                <span className='font-medium'>{tab.name}</span>\n              </div>\n\n              {/* Small dot indicator */}\n              {activeTab === tab.id ? (\n                <motion.div\n                  layoutId='activeDot'\n                  className='absolute right-3 w-2 h-2 rounded-full bg-white'\n                  initial={{ scale: 0 }}\n                  animate={{ scale: 1 }}\n                  transition={{ delay: 0.1 }}\n                />\n              ) : (\n                <div className='absolute right-3 w-2 h-2 rounded-full bg-gray-400/0 group-hover:bg-gray-400/30 transition-colors' />\n              )}\n            </button>\n          ))}\n        </div>\n\n        {/* Content area */}\n        <div className='flex-1 relative rounded-xl bg-white/80 dark:bg-gray-900/80 backdrop-filter backdrop-blur-lg shadow-lg overflow-hidden'>\n          {/* Loading overlay */}\n          <AnimatePresence>\n            {isLoading && (\n              <motion.div\n                key='loader'\n                className='absolute inset-0 z-20 flex items-center justify-center bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm'\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 0.7 }}\n                exit={{ opacity: 0 }}\n                transition={{ duration: 0.2 }}\n              >\n                <svg\n                  className='animate-spin h-8 w-8 text-indigo-600'\n                  xmlns='http://www.w3.org/2000/svg'\n                  fill='none'\n                  viewBox='0 0 24 24'\n                >\n                  <circle\n                    className='opacity-25'\n                    cx='12'\n                    cy='12'\n                    r='10'\n                    stroke='currentColor'\n                    strokeWidth='4'\n                  ></circle>\n                  <path\n                    className='opacity-75'\n                    fill='currentColor'\n                    d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'\n                  ></path>\n                </svg>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Tab content with animations */}\n          <AnimatePresence mode='wait'>\n            <motion.div\n              key={activeTab}\n              initial={{ opacity: 0, x: 10 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: -10 }}\n              transition={{ duration: 0.3 }}\n              className='p-6 h-64 overflow-y-auto'\n            >\n              <h3 className='text-lg font-semibold flex items-center gap-2 mb-4 text-gray-900 dark:text-white'>\n                <span>{tabs.find((t) => t.id === activeTab)?.icon}</span>\n                <span>{tabs.find((t) => t.id === activeTab)?.name}</span>\n              </h3>\n              <div className='prose dark:prose-invert'>\n                {tabs.find((tab) => tab.id === activeTab)?.content ||\n                  tabs[0].content}\n              </div>\n            </motion.div>\n          </AnimatePresence>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "type": "registry:ui"}]}